"""
Cliente para API do CVAT
"""
import requests
import json
from typing import Dict, List, Optional, Any
from loguru import logger
from config import settings

class CVATClient:
    def __init__(self):
        self.base_url = settings.CVAT_URL
        self.session = requests.Session()
        self.auth_token = None
        
    def authenticate(self, username: str = None, password: str = None) -> bool:
        """Autentica no CVAT"""
        try:
            username = username or settings.CVAT_USERNAME
            password = password or settings.CVAT_PASSWORD
            
            # Tentar autenticação por token primeiro
            if settings.CVAT_API_TOKEN:
                self.session.headers.update({
                    'Authorization': f'Token {settings.CVAT_API_TOKEN}'
                })
                return self._test_authentication()
            
            # Autenticação por usuário/senha
            auth_url = f"{self.base_url}/api/auth/login"
            auth_data = {
                'username': username,
                'password': password
            }
            
            response = self.session.post(auth_url, json=auth_data)
            
            if response.status_code == 200:
                # Obter token da resposta
                token_data = response.json()
                if 'key' in token_data:
                    self.auth_token = token_data['key']
                    self.session.headers.update({
                        'Authorization': f'Token {self.auth_token}'
                    })
                    logger.info("Autenticação no CVAT realizada com sucesso")
                    return True
                    
            logger.error(f"Falha na autenticação: {response.status_code}")
            return False
            
        except Exception as e:
            logger.error(f"Erro na autenticação: {e}")
            return False
    
    def _test_authentication(self) -> bool:
        """Testa se a autenticação está funcionando"""
        try:
            response = self.session.get(f"{self.base_url}/api/users/self")
            return response.status_code == 200
        except:
            return False
    
    def get_projects(self) -> List[Dict]:
        """Obtém lista de projetos"""
        try:
            response = self.session.get(f"{self.base_url}/api/projects")
            if response.status_code == 200:
                return response.json()['results']
            return []
        except Exception as e:
            logger.error(f"Erro ao obter projetos: {e}")
            return []
    
    def get_tasks(self, project_id: int = None) -> List[Dict]:
        """Obtém lista de tarefas"""
        try:
            url = f"{self.base_url}/api/tasks"
            if project_id:
                url += f"?project_id={project_id}"
                
            response = self.session.get(url)
            if response.status_code == 200:
                return response.json()['results']
            return []
        except Exception as e:
            logger.error(f"Erro ao obter tarefas: {e}")
            return []
    
    def get_jobs(self, task_id: int) -> List[Dict]:
        """Obtém lista de jobs de uma tarefa"""
        try:
            response = self.session.get(f"{self.base_url}/api/tasks/{task_id}/jobs")
            if response.status_code == 200:
                return response.json()['results']
            return []
        except Exception as e:
            logger.error(f"Erro ao obter jobs: {e}")
            return []
    
    def get_job_annotations(self, job_id: int) -> Dict:
        """Obtém anotações de um job"""
        try:
            response = self.session.get(f"{self.base_url}/api/jobs/{job_id}/annotations")
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            logger.error(f"Erro ao obter anotações do job {job_id}: {e}")
            return {}
    
    def update_job_annotations(self, job_id: int, annotations: Dict) -> bool:
        """Atualiza anotações de um job"""
        try:
            url = f"{self.base_url}/api/jobs/{job_id}/annotations"
            
            # Configurar headers
            headers = {
                'Content-Type': 'application/json',
            }
            
            response = self.session.patch(url, json=annotations, headers=headers)
            
            if response.status_code in [200, 204]:
                logger.info(f"Anotações do job {job_id} atualizadas com sucesso")
                return True
            else:
                logger.error(f"Erro ao atualizar anotações: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Erro ao atualizar anotações do job {job_id}: {e}")
            return False
    
    def get_job_frames(self, job_id: int) -> List[Dict]:
        """Obtém informações dos frames de um job"""
        try:
            response = self.session.get(f"{self.base_url}/api/jobs/{job_id}")
            if response.status_code == 200:
                job_data = response.json()
                return [{
                    'frame_id': i,
                    'frame_number': job_data['start_frame'] + i
                } for i in range(job_data['stop_frame'] - job_data['start_frame'] + 1)]
            return []
        except Exception as e:
            logger.error(f"Erro ao obter frames do job {job_id}: {e}")
            return []
    
    def download_frame(self, job_id: int, frame_number: int, save_path: str) -> bool:
        """Baixa um frame específico"""
        try:
            url = f"{self.base_url}/api/jobs/{job_id}/data"
            params = {
                'type': 'frame',
                'number': frame_number,
                'quality': 'original'
            }
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                return True
            return False
            
        except Exception as e:
            logger.error(f"Erro ao baixar frame {frame_number}: {e}")
            return False
    
    def create_annotation(self, label_id: int, frame: int, points: List[float], 
                         annotation_type: str = "rectangle") -> Dict:
        """Cria uma nova anotação"""
        annotation = {
            "type": annotation_type,
            "frame": frame,
            "label_id": label_id,
            "points": points,
            "occluded": False,
            "attributes": []
        }
        return annotation
