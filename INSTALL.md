# Guia de Instalação - Bot de Automação CVAT

## Pré-requisitos

### Sistema Operacional
- Linux (Ubuntu 18.04+, CentOS 7+)
- macOS (10.14+)
- Windows 10+ (com WSL recomendado)

### Software Necessário
- Python 3.8 ou superior
- pip (gerenciador de pacotes Python)
- Git
- Navegador web (Chrome, Firefox, ou Edge)

### Hardware Recomendado
- **RAM**: Mínimo 4GB, recomendado 8GB+
- **CPU**: Processador multi-core
- **GPU**: Opcional, mas recomendado para treinamento (NVIDIA com CUDA)
- **Armazenamento**: 2GB livres para instalação + espaço para dados

## Instalação Rápida

### 1. Clone o Repositório
```bash
git clone <url-do-repositorio>
cd cvat-automation-bot
```

### 2. Execute o Script de Configuração
```bash
# Linux/macOS
chmod +x scripts/setup.sh
./scripts/setup.sh

# Windows (PowerShell)
python scripts/setup.py
```

### 3. Configure as C<PERSON><PERSON>ciais
```bash
# Copie o arquivo de exemplo
cp .env.example .env

# Edite com suas configurações
nano .env  # ou use seu editor preferido
```

### 4. Teste a Instalação
```bash
python tests/test_basic.py
```

## Instalação Manual

### 1. Criar Ambiente Virtual (Recomendado)
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# ou
venv\Scripts\activate     # Windows
```

### 2. Instalar Dependências
```bash
pip install -r requirements.txt
```

### 3. Configurar Variáveis de Ambiente
Edite o arquivo `.env`:
```env
CVAT_URL=http://localhost:8080
CVAT_USERNAME=seu_usuario
CVAT_PASSWORD=sua_senha
BROWSER_TYPE=chrome
HEADLESS_MODE=false
CONFIDENCE_THRESHOLD=0.8
```

### 4. Criar Diretórios
```bash
mkdir -p models training_data/raw training_data/annotated logs temp_frames
```

## Configuração do CVAT

### 1. Instalar CVAT
Se você ainda não tem o CVAT instalado:

```bash
# Usando Docker (recomendado)
git clone https://github.com/openvinotoolkit/cvat
cd cvat
docker-compose up -d
```

### 2. Criar Usuário no CVAT
1. Acesse `http://localhost:8080`
2. Registre um novo usuário
3. Anote as credenciais para usar no bot

### 3. Obter Token de API (Opcional)
1. Faça login no CVAT
2. Vá para Settings > API Token
3. Gere um novo token
4. Adicione ao arquivo `.env`: `CVAT_API_TOKEN=seu_token`

## Configuração de Navegadores

### Chrome (Recomendado)
```bash
# Ubuntu/Debian
sudo apt-get install google-chrome-stable

# CentOS/RHEL
sudo yum install google-chrome-stable

# macOS (usando Homebrew)
brew install --cask google-chrome
```

### Firefox
```bash
# Ubuntu/Debian
sudo apt-get install firefox

# CentOS/RHEL
sudo yum install firefox

# macOS
brew install --cask firefox
```

## Configuração de GPU (Opcional)

Para acelerar o treinamento com GPU NVIDIA:

### 1. Instalar CUDA
```bash
# Verificar se NVIDIA GPU está disponível
nvidia-smi

# Instalar CUDA Toolkit (versão compatível com TensorFlow)
# Consulte: https://www.tensorflow.org/install/gpu
```

### 2. Instalar TensorFlow GPU
```bash
pip install tensorflow-gpu==2.13.0
```

### 3. Verificar Instalação
```python
import tensorflow as tf
print("GPUs disponíveis:", tf.config.list_physical_devices('GPU'))
```

## Solução de Problemas

### Erro: "ModuleNotFoundError"
```bash
# Certifique-se de que o ambiente virtual está ativado
source venv/bin/activate

# Reinstale as dependências
pip install -r requirements.txt
```

### Erro: "WebDriver not found"
```bash
# O bot baixa automaticamente os drivers
# Se houver problemas, instale manualmente:

# Chrome
pip install webdriver-manager
python -c "from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()"

# Firefox
python -c "from webdriver_manager.firefox import GeckoDriverManager; GeckoDriverManager().install()"
```

### Erro: "CVAT connection failed"
1. Verifique se o CVAT está rodando: `curl http://localhost:8080`
2. Confirme as credenciais no arquivo `.env`
3. Teste o login manual no navegador

### Erro: "Permission denied"
```bash
# Linux/macOS - dar permissões aos scripts
chmod +x scripts/*.sh

# Verificar permissões dos diretórios
ls -la
```

### Problemas de Memória
1. Reduza o `BATCH_SIZE` no config.py
2. Use `IMAGE_SIZE` menor (ex: (128, 128))
3. Ative `HEADLESS_MODE=true`

## Verificação da Instalação

### Teste Completo
```bash
# Executar todos os testes
python tests/test_basic.py

# Verificar configurações
python -c "from config import settings; print('Configuração OK')"

# Testar conectividade CVAT
python -c "from cvat_api.client import CVATClient; client = CVATClient(); print('CVAT OK' if client.authenticate() else 'CVAT Erro')"
```

### Teste de Exemplo
```bash
# Executar exemplo básico
python examples/example_usage.py --exemplo config

# Criar dados de exemplo
python examples/example_usage.py --exemplo treinamento
```

## Próximos Passos

Após a instalação bem-sucedida:

1. **Prepare seus dados**: Organize imagens brutas e anotadas
2. **Treine o modelo**: `python main.py train --raw-images ... --annotated-images ...`
3. **Liste jobs**: `python main.py list-jobs`
4. **Execute anotação**: `python main.py annotate --job-ids ...`

## Suporte

- 📖 Consulte o [README.md](README.md) para uso detalhado
- 🐛 Reporte bugs criando uma issue
- 💬 Para dúvidas, consulte a documentação do CVAT
- 📝 Logs detalhados em `./logs/cvat_bot.log`

## Atualizações

Para atualizar o bot:
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```
