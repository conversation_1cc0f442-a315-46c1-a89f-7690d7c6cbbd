2025-05-30 16:00:35 | INFO | === ESTATÍSTICAS DO SISTEMA ===
2025-05-30 16:00:35 | INFO | Dados de treinamento:
2025-05-30 16:00:35 | INFO |   - Imagens brutas: 0
2025-05-30 16:00:35 | INFO |   - Imagens anotadas: 0
2025-05-30 16:00:35 | INFO |   - Total de anotações: 0
2025-05-30 16:00:35 | INFO |   - Labels: []
2025-05-30 16:00:35 | INFO | Modelo treinado: Não
2025-05-30 16:00:35 | ERROR | Erro na autenticação: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/auth/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fe4f8b185c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-05-30 16:00:40 | INFO | Imagens de exemplo criadas em ./exemplos/
2025-05-30 16:00:40 | INFO | === INICIANDO TREINAMENTO DO MODELO ===
2025-05-30 16:00:40 | INFO | Adicionando imagens ao conjunto de treinamento...
2025-05-30 16:00:40 | INFO | Imagem copiada: raw_image_0000_exemplo_raw.jpg
2025-05-30 16:00:40 | INFO | Imagem anotada copiada: annotated_image_0000_exemplo_anotado.jpg
2025-05-30 16:00:40 | INFO | Extraindo anotações das imagens...
2025-05-30 16:00:40 | INFO | Processando imagem anotada: annotated_image_0000_exemplo_anotado.jpg
2025-05-30 16:00:40 | INFO | Extraídas 1 anotações de annotated_image_0000_exemplo_anotado.jpg
2025-05-30 16:00:40 | INFO | Total de 1 anotações extraídas
2025-05-30 16:00:40 | INFO | Preparando dataset para treinamento...
2025-05-30 16:00:40 | INFO | Labels encontrados: ['red']
2025-05-30 16:00:40 | INFO | Dataset preparado: 1 amostras, 1 classes
2025-05-30 16:00:40 | INFO | Iniciando treinamento do modelo...
2025-05-30 16:00:40 | INFO | Iniciando treinamento do modelo (versão simplificada)...
2025-05-30 16:00:40 | WARNING | Nenhum dado de treinamento encontrado, criando modelo padrão
2025-05-30 16:00:40 | INFO | Criando modelo simples para 1 classes
2025-05-30 16:00:40 | INFO | Modelo salvo em ./models/trained_model.h5
2025-05-30 16:00:40 | INFO | Treinamento concluído com sucesso!
2025-05-30 16:00:40 | INFO | === TREINAMENTO CONCLUÍDO COM SUCESSO ===
2025-05-30 16:00:40 | INFO | Treinamento de exemplo concluído!
