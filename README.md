# Bot de Automação CVAT

Este bot automatiza o processo de marcação de labels no CVAT usando machine learning para aprender padrões de anotação e aplicá-los automaticamente.

## Funcionalidades

- 🤖 **Aprendizado Automático**: Aprende padrões de anotação a partir de imagens de exemplo
- 🎯 **Detecção Inteligente**: Usa TensorFlow para detectar objetos em imagens
- 🌐 **Automação Web**: Controla o navegador para criar anotações no CVAT
- 📊 **API Integration**: Integra com a API do CVAT para operações eficientes
- 📈 **Estatísticas**: Fornece relatórios detalhados do processo de anotação

## Instalação

1. **Clone o repositório**:
```bash
git clone <url-do-repositorio>
cd cvat-automation-bot
```

2. **Instale as dependências**:
```bash
pip install -r requirements.txt
```

3. **Configure as variáveis de ambiente**:
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

4. **Instale o driver do navegador**:
O bot usa webdriver-manager para baixar automaticamente os drivers necessários.

## Configuração

### Arquivo .env

Configure as seguintes variáveis no arquivo `.env`:

```env
# CVAT
CVAT_URL=http://localhost:8080
CVAT_USERNAME=seu_usuario
CVAT_PASSWORD=sua_senha

# Navegador
BROWSER_TYPE=chrome  # chrome, firefox, edge
HEADLESS_MODE=false  # true para executar sem interface gráfica

# Modelo
CONFIDENCE_THRESHOLD=0.8  # Limiar de confiança para detecções
```

## Uso

### 1. Treinar o Modelo

Primeiro, você precisa treinar o modelo com imagens de exemplo:

```bash
python main.py train \
  --raw-images ./exemplos/imagem1.jpg ./exemplos/imagem2.jpg \
  --annotated-images ./exemplos/anotada1.jpg ./exemplos/anotada2.jpg
```

**Estrutura das imagens**:
- **Imagens brutas**: Fotos originais dos objetos
- **Imagens anotadas**: Mesmas fotos com marcações coloridas indicando onde estão os objetos

### 2. Listar Jobs Disponíveis

```bash
# Listar todos os jobs
python main.py list-jobs

# Listar jobs de um projeto específico
python main.py list-jobs --project-id 1

# Listar jobs de uma tarefa específica
python main.py list-jobs --task-id 5
```

### 3. Anotar Jobs Automaticamente

```bash
# Anotar jobs específicos
python main.py annotate --job-ids 1 2 3

# Anotar com mapeamento de labels personalizado
python main.py annotate --job-ids 1 2 3 --label-mappings ./label_mappings.json
```

**Exemplo de label_mappings.json**:
```json
{
  "detected_object": "pessoa",
  "red": "veiculo",
  "blue": "animal"
}
```

### 4. Ver Estatísticas

```bash
python main.py stats
```

### 5. Limpar Dados de Treinamento

```bash
python main.py clean
```

## Estrutura do Projeto

```
cvat-automation-bot/
├── main.py                    # Script principal
├── config.py                  # Configurações
├── requirements.txt           # Dependências
├── models/
│   └── pattern_detector.py    # Modelo de ML
├── cvat_api/
│   └── client.py              # Cliente da API CVAT
├── automation/
│   ├── browser_controller.py  # Controle do navegador
│   └── label_annotator.py     # Lógica de anotação
├── data/
│   └── training_manager.py    # Gerenciamento de dados
├── training_data/             # Dados de treinamento
│   ├── raw/                   # Imagens originais
│   └── annotated/             # Imagens anotadas
├── models/                    # Modelos treinados
└── logs/                      # Arquivos de log
```

## Como Funciona

### 1. Fase de Treinamento

1. **Coleta de Dados**: O bot recebe imagens originais e suas versões anotadas
2. **Extração de Padrões**: Analisa as imagens anotadas para identificar padrões de marcação
3. **Treinamento do Modelo**: Usa TensorFlow para treinar um modelo de detecção de objetos
4. **Validação**: Testa o modelo com dados de validação

### 2. Fase de Anotação

1. **Autenticação**: Conecta-se ao CVAT via API e navegador
2. **Navegação**: Acessa o job específico no CVAT
3. **Processamento**: Para cada frame:
   - Baixa a imagem
   - Aplica o modelo treinado
   - Identifica objetos com confiança acima do limiar
   - Cria anotações no navegador
4. **Salvamento**: Salva todas as anotações criadas

## Requisitos do Sistema

- Python 3.8+
- CVAT instalado e funcionando
- Navegador Chrome, Firefox ou Edge
- Pelo menos 4GB de RAM (recomendado 8GB)
- GPU opcional (para treinamento mais rápido)

## Troubleshooting

### Erro de Autenticação
- Verifique as credenciais no arquivo `.env`
- Certifique-se de que o CVAT está acessível na URL configurada

### Erro do WebDriver
- O bot baixa automaticamente os drivers necessários
- Certifique-se de que o navegador está instalado

### Modelo não Converge
- Adicione mais imagens de treinamento
- Verifique se as anotações estão claras e consistentes
- Ajuste os parâmetros de treinamento

### Performance Lenta
- Use modo headless (`HEADLESS_MODE=true`)
- Reduza o tamanho das imagens
- Use GPU para treinamento

## Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para detalhes.

## Suporte

Para suporte e dúvidas:
- Abra uma issue no GitHub
- Consulte os logs em `./logs/cvat_bot.log`
- Verifique a documentação do CVAT
