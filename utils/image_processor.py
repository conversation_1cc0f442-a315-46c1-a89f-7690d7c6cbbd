"""
Utilitários para processamento de imagens
"""
import cv2
import numpy as np
from typing import List, Tu<PERSON>, Dict, Optional
from pathlib import Path
import os
from loguru import logger

class ImageProcessor:
    """Classe para processamento de imagens"""
    
    @staticmethod
    def resize_image(image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """Redimensiona uma imagem mantendo a proporção"""
        try:
            height, width = image.shape[:2]
            target_width, target_height = target_size
            
            # Calcular proporção
            ratio = min(target_width / width, target_height / height)
            
            # Calcular novas dimensões
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            
            # Redimensionar
            resized = cv2.resize(image, (new_width, new_height))
            
            # Criar imagem com padding se necessário
            if new_width != target_width or new_height != target_height:
                # Criar imagem preta do tamanho alvo
                padded = np.zeros((target_height, target_width, 3), dtype=np.uint8)
                
                # Calcular posição para centralizar
                y_offset = (target_height - new_height) // 2
                x_offset = (target_width - new_width) // 2
                
                # Colocar imagem redimensionada no centro
                padded[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
                
                return padded
            
            return resized
            
        except Exception as e:
            logger.error(f"Erro ao redimensionar imagem: {e}")
            return image
    
    @staticmethod
    def extract_color_regions(image: np.ndarray, color_ranges: Dict[str, Tuple]) -> Dict[str, List[Tuple]]:
        """Extrai regiões baseadas em cores específicas"""
        try:
            # Converter para HSV
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            regions = {}
            
            for color_name, (lower, upper) in color_ranges.items():
                # Criar máscara
                lower_np = np.array(lower)
                upper_np = np.array(upper)
                mask = cv2.inRange(hsv, lower_np, upper_np)
                
                # Encontrar contornos
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # Extrair bounding boxes
                bboxes = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 50:  # Filtrar áreas muito pequenas
                        x, y, w, h = cv2.boundingRect(contour)
                        bboxes.append((x, y, x + w, y + h))
                
                regions[color_name] = bboxes
            
            return regions
            
        except Exception as e:
            logger.error(f"Erro ao extrair regiões coloridas: {e}")
            return {}
    
    @staticmethod
    def enhance_image(image: np.ndarray) -> np.ndarray:
        """Melhora a qualidade da imagem"""
        try:
            # Converter para LAB
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            
            # Aplicar CLAHE no canal L
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            lab[:, :, 0] = clahe.apply(lab[:, :, 0])
            
            # Converter de volta para BGR
            enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            
            return enhanced
            
        except Exception as e:
            logger.error(f"Erro ao melhorar imagem: {e}")
            return image
    
    @staticmethod
    def create_annotation_overlay(image: np.ndarray, annotations: List[Dict]) -> np.ndarray:
        """Cria overlay com anotações visualizadas"""
        try:
            overlay = image.copy()
            
            # Cores para diferentes labels
            colors = {
                'pessoa': (0, 255, 0),      # Verde
                'veiculo': (255, 0, 0),     # Azul
                'animal': (0, 0, 255),      # Vermelho
                'objeto': (255, 255, 0),    # Ciano
                'default': (128, 128, 128)  # Cinza
            }
            
            for ann in annotations:
                # Obter coordenadas
                if 'bbox' in ann:
                    x1, y1, x2, y2 = ann['bbox']
                elif 'points' in ann and len(ann['points']) >= 4:
                    x1, y1, x2, y2 = ann['points'][:4]
                else:
                    continue
                
                # Obter cor baseada no label
                label = ann.get('label', 'default')
                color = colors.get(label, colors['default'])
                
                # Desenhar retângulo
                cv2.rectangle(overlay, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
                
                # Adicionar texto do label
                confidence = ann.get('confidence', 1.0)
                text = f"{label}: {confidence:.2f}"
                
                # Calcular posição do texto
                text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                text_x = int(x1)
                text_y = int(y1) - 10 if y1 > 20 else int(y1) + text_size[1] + 10
                
                # Desenhar fundo do texto
                cv2.rectangle(overlay, 
                            (text_x, text_y - text_size[1] - 5),
                            (text_x + text_size[0] + 5, text_y + 5),
                            color, -1)
                
                # Desenhar texto
                cv2.putText(overlay, text, (text_x + 2, text_y - 2),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return overlay
            
        except Exception as e:
            logger.error(f"Erro ao criar overlay: {e}")
            return image
    
    @staticmethod
    def validate_image(image_path: str) -> bool:
        """Valida se uma imagem é válida"""
        try:
            if not os.path.exists(image_path):
                return False
            
            # Tentar carregar a imagem
            image = cv2.imread(image_path)
            if image is None:
                return False
            
            # Verificar dimensões mínimas
            height, width = image.shape[:2]
            if height < 10 or width < 10:
                return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def batch_process_images(image_paths: List[str], 
                           output_dir: str,
                           target_size: Tuple[int, int] = None,
                           enhance: bool = False) -> List[str]:
        """Processa um lote de imagens"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            processed_paths = []
            
            for i, image_path in enumerate(image_paths):
                if not ImageProcessor.validate_image(image_path):
                    logger.warning(f"Imagem inválida ignorada: {image_path}")
                    continue
                
                # Carregar imagem
                image = cv2.imread(image_path)
                
                # Aplicar melhorias se solicitado
                if enhance:
                    image = ImageProcessor.enhance_image(image)
                
                # Redimensionar se solicitado
                if target_size:
                    image = ImageProcessor.resize_image(image, target_size)
                
                # Salvar imagem processada
                filename = f"processed_{i:04d}_{Path(image_path).name}"
                output_path = os.path.join(output_dir, filename)
                
                if cv2.imwrite(output_path, image):
                    processed_paths.append(output_path)
                    logger.info(f"Imagem processada: {filename}")
                else:
                    logger.error(f"Erro ao salvar imagem: {filename}")
            
            return processed_paths
            
        except Exception as e:
            logger.error(f"Erro no processamento em lote: {e}")
            return []
    
    @staticmethod
    def create_training_pairs(raw_images: List[str], 
                            annotated_images: List[str]) -> List[Tuple[str, str]]:
        """Cria pares de imagens para treinamento"""
        try:
            pairs = []
            
            # Tentar fazer correspondência por nome
            for raw_path in raw_images:
                raw_name = Path(raw_path).stem
                
                # Procurar imagem anotada correspondente
                for ann_path in annotated_images:
                    ann_name = Path(ann_path).stem
                    
                    # Verificar se os nomes correspondem (ignorando sufixos como "_anotado")
                    if raw_name in ann_name or ann_name in raw_name:
                        pairs.append((raw_path, ann_path))
                        break
            
            # Se não conseguiu fazer correspondência por nome, fazer por ordem
            if not pairs and len(raw_images) == len(annotated_images):
                pairs = list(zip(raw_images, annotated_images))
            
            logger.info(f"Criados {len(pairs)} pares de treinamento")
            return pairs
            
        except Exception as e:
            logger.error(f"Erro ao criar pares de treinamento: {e}")
            return []
