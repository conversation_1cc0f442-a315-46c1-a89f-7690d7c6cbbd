#!/usr/bin/env python3
"""
Bot de Automação CVAT - Script Principal
"""
import argparse
import sys
import os
from pathlib import Path
from loguru import logger
from typing import List, Dict

# Configurar logging
from config import settings
logger.add(
    settings.LOG_FILE,
    rotation="10 MB",
    level=settings.LOG_LEVEL,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
)

# Imports dos módulos
from models.pattern_detector import PatternDetector
from data.training_manager import TrainingDataManager
# from automation.label_annotator import LabelAnnotator  # Comentado temporariamente
from cvat_api.client import CVATClient

class CVATBot:
    def __init__(self):
        self.pattern_detector = PatternDetector()
        self.training_manager = TrainingDataManager()
        # self.label_annotator = LabelAnnotator()  # Comentado temporariamente
        self.cvat_client = CVATClient()

    def train_model(self, raw_images: List[str], annotated_images: List[str]) -> bool:
        """Treina o modelo com as imagens fornecidas"""
        try:
            logger.info("=== INICIANDO TREINAMENTO DO MODELO ===")

            # Adicionar imagens ao conjunto de treinamento
            logger.info("Adicionando imagens ao conjunto de treinamento...")
            if not self.training_manager.add_training_images(raw_images, annotated_images):
                logger.error("Falha ao adicionar imagens de treinamento")
                return False

            # Extrair anotações das imagens anotadas
            logger.info("Extraindo anotações das imagens...")
            annotations = self.training_manager.extract_annotations_from_images()
            if not annotations:
                logger.error("Nenhuma anotação extraída")
                return False

            # Preparar dataset
            logger.info("Preparando dataset para treinamento...")
            X, y = self.training_manager.prepare_training_dataset()
            if X.size == 0:
                logger.error("Dataset vazio")
                return False

            # Treinar modelo
            logger.info("Iniciando treinamento do modelo...")
            self.pattern_detector.train_model(settings.TRAINING_DATA_PATH)

            logger.info("=== TREINAMENTO CONCLUÍDO COM SUCESSO ===")
            return True

        except Exception as e:
            logger.error(f"Erro durante o treinamento: {e}")
            return False

    def annotate_jobs(self, job_ids: List[int], label_mappings: Dict[str, str] = None) -> bool:
        """Anota jobs automaticamente (funcionalidade temporariamente desabilitada)"""
        try:
            logger.info(f"=== ANOTAÇÃO AUTOMÁTICA SOLICITADA PARA {len(job_ids)} JOBS ===")
            logger.warning("Funcionalidade de anotação automática temporariamente desabilitada")
            logger.info("Para habilitar, instale as dependências: pip install selenium webdriver-manager")
            logger.info("Jobs solicitados:", job_ids)
            if label_mappings:
                logger.info("Mapeamentos de labels:", label_mappings)

            return False

        except Exception as e:
            logger.error(f"Erro durante a anotação: {e}")
            return False

    def get_available_jobs(self, project_id: int = None, task_id: int = None) -> List[Dict]:
        """Obtém lista de jobs disponíveis"""
        try:
            if not self.cvat_client.authenticate():
                logger.error("Falha na autenticação")
                return []

            jobs = []

            if task_id:
                # Obter jobs de uma tarefa específica
                task_jobs = self.cvat_client.get_jobs(task_id)
                jobs.extend(task_jobs)
            elif project_id:
                # Obter todas as tarefas do projeto
                tasks = self.cvat_client.get_tasks(project_id)
                for task in tasks:
                    task_jobs = self.cvat_client.get_jobs(task['id'])
                    jobs.extend(task_jobs)
            else:
                # Obter todas as tarefas
                tasks = self.cvat_client.get_tasks()
                for task in tasks:
                    task_jobs = self.cvat_client.get_jobs(task['id'])
                    jobs.extend(task_jobs)

            return jobs

        except Exception as e:
            logger.error(f"Erro ao obter jobs: {e}")
            return []

    def show_statistics(self) -> None:
        """Mostra estatísticas do sistema"""
        try:
            logger.info("=== ESTATÍSTICAS DO SISTEMA ===")

            # Estatísticas de treinamento
            training_stats = self.training_manager.get_training_statistics()
            logger.info(f"Dados de treinamento:")
            logger.info(f"  - Imagens brutas: {training_stats.get('raw_images', 0)}")
            logger.info(f"  - Imagens anotadas: {training_stats.get('annotated_images', 0)}")
            logger.info(f"  - Total de anotações: {training_stats.get('total_annotations', 0)}")
            logger.info(f"  - Labels: {training_stats.get('labels', [])}")

            # Verificar se modelo existe
            model_exists = os.path.exists(settings.MODEL_PATH)
            logger.info(f"Modelo treinado: {'Sim' if model_exists else 'Não'}")

            # Estatísticas do CVAT
            if self.cvat_client.authenticate():
                projects = self.cvat_client.get_projects()
                tasks = self.cvat_client.get_tasks()
                logger.info(f"CVAT:")
                logger.info(f"  - Projetos: {len(projects)}")
                logger.info(f"  - Tarefas: {len(tasks)}")

        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")

def main():
    parser = argparse.ArgumentParser(description="Bot de Automação CVAT")
    subparsers = parser.add_subparsers(dest='command', help='Comandos disponíveis')

    # Comando de treinamento
    train_parser = subparsers.add_parser('train', help='Treinar modelo')
    train_parser.add_argument('--raw-images', nargs='+', required=True,
                             help='Caminhos para imagens brutas')
    train_parser.add_argument('--annotated-images', nargs='+', required=True,
                             help='Caminhos para imagens anotadas')

    # Comando de anotação
    annotate_parser = subparsers.add_parser('annotate', help='Anotar jobs')
    annotate_parser.add_argument('--job-ids', nargs='+', type=int, required=True,
                                help='IDs dos jobs para anotar')
    annotate_parser.add_argument('--label-mappings', type=str,
                                help='Arquivo JSON com mapeamento de labels')

    # Comando para listar jobs
    list_parser = subparsers.add_parser('list-jobs', help='Listar jobs disponíveis')
    list_parser.add_argument('--project-id', type=int, help='ID do projeto')
    list_parser.add_argument('--task-id', type=int, help='ID da tarefa')

    # Comando de estatísticas
    subparsers.add_parser('stats', help='Mostrar estatísticas')

    # Comando de limpeza
    subparsers.add_parser('clean', help='Limpar dados de treinamento')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    bot = CVATBot()

    try:
        if args.command == 'train':
            success = bot.train_model(args.raw_images, args.annotated_images)
            sys.exit(0 if success else 1)

        elif args.command == 'annotate':
            label_mappings = None
            if args.label_mappings and os.path.exists(args.label_mappings):
                import json
                with open(args.label_mappings, 'r') as f:
                    label_mappings = json.load(f)

            success = bot.annotate_jobs(args.job_ids, label_mappings)
            sys.exit(0 if success else 1)

        elif args.command == 'list-jobs':
            jobs = bot.get_available_jobs(args.project_id, args.task_id)

            if jobs:
                logger.info(f"Jobs encontrados: {len(jobs)}")
                for job in jobs:
                    logger.info(f"  Job ID: {job['id']}, Task: {job.get('task_id', 'N/A')}, "
                              f"Status: {job.get('status', 'N/A')}")
            else:
                logger.info("Nenhum job encontrado")

        elif args.command == 'stats':
            bot.show_statistics()

        elif args.command == 'clean':
            if bot.training_manager.clear_training_data():
                logger.info("Dados de treinamento limpos com sucesso")
            else:
                logger.error("Erro ao limpar dados de treinamento")
                sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Operação cancelada pelo usuário")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Erro inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
