#!/bin/bash

# Script de configuração do Bot de Automação CVAT

echo "=== Configuração do Bot de Automação CVAT ==="

# Verificar se Python está instalado
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 não encontrado. Por favor, instale Python 3.8 ou superior."
    exit 1
fi

echo "✅ Python encontrado: $(python3 --version)"

# Verificar se pip está instalado
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 não encontrado. Por favor, instale pip."
    exit 1
fi

echo "✅ pip encontrado: $(pip3 --version)"

# Criar ambiente virtual (opcional)
read -p "Deseja criar um ambiente virtual? (y/n): " create_venv
if [[ $create_venv == "y" || $create_venv == "Y" ]]; then
    echo "📦 Criando ambiente virtual..."
    python3 -m venv venv
    source venv/bin/activate
    echo "✅ Ambiente virtual criado e ativado"
fi

# Instalar dependências
echo "📦 Instalando dependências..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependências instaladas com sucesso"
else
    echo "❌ Erro ao instalar dependências"
    exit 1
fi

# Criar arquivo .env se não existir
if [ ! -f .env ]; then
    echo "📝 Criando arquivo .env..."
    cp .env.example .env
    echo "✅ Arquivo .env criado. Por favor, configure suas credenciais."
else
    echo "ℹ️  Arquivo .env já existe"
fi

# Criar diretórios necessários
echo "📁 Criando diretórios..."
mkdir -p models
mkdir -p training_data/raw
mkdir -p training_data/annotated
mkdir -p logs
mkdir -p temp_frames
mkdir -p exemplos

echo "✅ Diretórios criados"

# Verificar se CVAT está acessível
echo "🔍 Verificando conectividade com CVAT..."
source .env 2>/dev/null || true

if [ -n "$CVAT_URL" ]; then
    if curl -s --head "$CVAT_URL" | head -n 1 | grep -q "200 OK"; then
        echo "✅ CVAT acessível em $CVAT_URL"
    else
        echo "⚠️  CVAT não acessível em $CVAT_URL. Verifique a configuração."
    fi
else
    echo "⚠️  CVAT_URL não configurado no arquivo .env"
fi

# Verificar navegadores disponíveis
echo "🌐 Verificando navegadores disponíveis..."

if command -v google-chrome &> /dev/null || command -v chromium-browser &> /dev/null; then
    echo "✅ Chrome/Chromium encontrado"
fi

if command -v firefox &> /dev/null; then
    echo "✅ Firefox encontrado"
fi

# Testar importações Python
echo "🐍 Testando importações Python..."
python3 -c "
import cv2
import tensorflow as tf
import selenium
import requests
import numpy as np
print('✅ Todas as importações funcionando')
" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Teste de importações passou"
else
    echo "❌ Erro nas importações. Verifique a instalação das dependências."
fi

echo ""
echo "=== CONFIGURAÇÃO CONCLUÍDA ==="
echo ""
echo "📋 Próximos passos:"
echo "1. Configure o arquivo .env com suas credenciais CVAT"
echo "2. Prepare suas imagens de treinamento"
echo "3. Execute: python main.py train --raw-images ... --annotated-images ..."
echo "4. Execute: python main.py annotate --job-ids ..."
echo ""
echo "📖 Para mais informações, consulte o README.md"
echo ""

# Mostrar exemplo de uso
echo "💡 Exemplo de uso:"
echo "python main.py train --raw-images ./exemplos/img1.jpg --annotated-images ./exemplos/img1_anotado.jpg"
echo "python main.py list-jobs"
echo "python main.py annotate --job-ids 1 2 3"
echo ""

echo "🎉 Configuração concluída com sucesso!"
