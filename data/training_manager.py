"""
Gerenciador de Dados de Treinamento
"""
import os
import cv2
import json
import numpy as np
from typing import List, Dict, Tuple, Any
from loguru import logger
import shutil
from pathlib import Path

from config import settings

class TrainingDataManager:
    def __init__(self):
        self.raw_images_path = Path(settings.RAW_IMAGES_PATH)
        self.annotated_images_path = Path(settings.ANNOTATED_IMAGES_PATH)
        self.training_data_path = Path(settings.TRAINING_DATA_PATH)
        
        # Criar diretórios se não existirem
        self.raw_images_path.mkdir(parents=True, exist_ok=True)
        self.annotated_images_path.mkdir(parents=True, exist_ok=True)
        self.training_data_path.mkdir(parents=True, exist_ok=True)
    
    def add_training_images(self, image_paths: List[str], 
                           annotated_image_paths: List[str] = None) -> bool:
        """Adiciona imagens ao conjunto de treinamento"""
        try:
            if len(image_paths) == 0:
                logger.warning("Nenhuma imagem fornecida")
                return False
            
            # Copiar imagens brutas
            for i, image_path in enumerate(image_paths):
                if os.path.exists(image_path):
                    filename = f"raw_image_{i:04d}_{Path(image_path).name}"
                    dest_path = self.raw_images_path / filename
                    shutil.copy2(image_path, dest_path)
                    logger.info(f"Imagem copiada: {filename}")
                else:
                    logger.warning(f"Imagem não encontrada: {image_path}")
            
            # Copiar imagens anotadas se fornecidas
            if annotated_image_paths:
                for i, annotated_path in enumerate(annotated_image_paths):
                    if os.path.exists(annotated_path):
                        filename = f"annotated_image_{i:04d}_{Path(annotated_path).name}"
                        dest_path = self.annotated_images_path / filename
                        shutil.copy2(annotated_path, dest_path)
                        logger.info(f"Imagem anotada copiada: {filename}")
                    else:
                        logger.warning(f"Imagem anotada não encontrada: {annotated_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao adicionar imagens de treinamento: {e}")
            return False
    
    def extract_annotations_from_images(self) -> List[Dict]:
        """Extrai anotações das imagens anotadas"""
        annotations = []
        
        try:
            annotated_files = list(self.annotated_images_path.glob("*.jpg")) + \
                            list(self.annotated_images_path.glob("*.png"))
            
            for image_path in annotated_files:
                logger.info(f"Processando imagem anotada: {image_path.name}")
                
                # Carregar imagem
                image = cv2.imread(str(image_path))
                if image is None:
                    continue
                
                # Extrair anotações usando detecção de contornos
                extracted_annotations = self._extract_annotations_from_image(image, str(image_path))
                
                if extracted_annotations:
                    annotations.extend(extracted_annotations)
                    logger.info(f"Extraídas {len(extracted_annotations)} anotações de {image_path.name}")
            
            # Salvar anotações em arquivo JSON
            annotations_file = self.training_data_path / "extracted_annotations.json"
            with open(annotations_file, 'w') as f:
                json.dump(annotations, f, indent=2)
            
            logger.info(f"Total de {len(annotations)} anotações extraídas")
            return annotations
            
        except Exception as e:
            logger.error(f"Erro ao extrair anotações: {e}")
            return []
    
    def _extract_annotations_from_image(self, image: np.ndarray, image_path: str) -> List[Dict]:
        """Extrai anotações de uma única imagem"""
        annotations = []
        
        try:
            # Converter para HSV para melhor detecção de cores
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Definir ranges de cores para diferentes tipos de anotações
            color_ranges = {
                'red': ([0, 50, 50], [10, 255, 255]),
                'green': ([40, 50, 50], [80, 255, 255]),
                'blue': ([100, 50, 50], [130, 255, 255]),
                'yellow': ([20, 50, 50], [30, 255, 255])
            }
            
            for color_name, (lower, upper) in color_ranges.items():
                # Criar máscara para a cor
                lower_np = np.array(lower)
                upper_np = np.array(upper)
                mask = cv2.inRange(hsv, lower_np, upper_np)
                
                # Encontrar contornos
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    # Filtrar contornos muito pequenos
                    area = cv2.contourArea(contour)
                    if area < 100:  # Área mínima
                        continue
                    
                    # Obter bounding box
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Filtrar bounding boxes muito pequenas
                    if w < 10 or h < 10:
                        continue
                    
                    annotation = {
                        'image_path': image_path,
                        'label': color_name,
                        'bbox': [x, y, x + w, y + h],
                        'area': area,
                        'confidence': 1.0  # Anotação manual tem confiança máxima
                    }
                    
                    annotations.append(annotation)
            
            return annotations
            
        except Exception as e:
            logger.error(f"Erro ao extrair anotações da imagem {image_path}: {e}")
            return []
    
    def prepare_training_dataset(self) -> Tuple[np.ndarray, np.ndarray]:
        """Prepara dataset para treinamento"""
        try:
            # Carregar anotações
            annotations_file = self.training_data_path / "extracted_annotations.json"
            if not annotations_file.exists():
                logger.error("Arquivo de anotações não encontrado. Execute extract_annotations_from_images primeiro.")
                return np.array([]), np.array([])
            
            with open(annotations_file, 'r') as f:
                annotations = json.load(f)
            
            if not annotations:
                logger.error("Nenhuma anotação encontrada")
                return np.array([]), np.array([])
            
            # Preparar dados
            images = []
            labels = []
            
            # Obter labels únicos
            unique_labels = list(set(ann['label'] for ann in annotations))
            label_to_id = {label: i for i, label in enumerate(unique_labels)}
            
            logger.info(f"Labels encontrados: {unique_labels}")
            
            for annotation in annotations:
                try:
                    # Carregar imagem
                    image = cv2.imread(annotation['image_path'])
                    if image is None:
                        continue
                    
                    # Extrair região da anotação
                    bbox = annotation['bbox']
                    x1, y1, x2, y2 = bbox
                    
                    # Validar coordenadas
                    h, w = image.shape[:2]
                    x1 = max(0, min(x1, w-1))
                    y1 = max(0, min(y1, h-1))
                    x2 = max(x1+1, min(x2, w))
                    y2 = max(y1+1, min(y2, h))
                    
                    # Extrair região
                    roi = image[y1:y2, x1:x2]
                    
                    if roi.size == 0:
                        continue
                    
                    # Redimensionar para tamanho padrão
                    roi_resized = cv2.resize(roi, settings.IMAGE_SIZE)
                    
                    # Converter BGR para RGB
                    roi_rgb = cv2.cvtColor(roi_resized, cv2.COLOR_BGR2RGB)
                    
                    # Normalizar
                    roi_normalized = roi_rgb.astype(np.float32) / 255.0
                    
                    images.append(roi_normalized)
                    labels.append(label_to_id[annotation['label']])
                    
                except Exception as e:
                    logger.error(f"Erro ao processar anotação: {e}")
                    continue
            
            if not images:
                logger.error("Nenhuma imagem válida processada")
                return np.array([]), np.array([])
            
            # Converter para arrays numpy
            X = np.array(images)
            y = np.array(labels)
            
            # Salvar mapeamento de labels
            label_mapping_file = self.training_data_path / "label_mapping.json"
            with open(label_mapping_file, 'w') as f:
                json.dump({
                    'label_to_id': label_to_id,
                    'id_to_label': {v: k for k, v in label_to_id.items()}
                }, f, indent=2)
            
            logger.info(f"Dataset preparado: {X.shape[0]} amostras, {len(unique_labels)} classes")
            return X, y
            
        except Exception as e:
            logger.error(f"Erro ao preparar dataset: {e}")
            return np.array([]), np.array([])
    
    def get_training_statistics(self) -> Dict[str, Any]:
        """Obtém estatísticas dos dados de treinamento"""
        try:
            stats = {
                'raw_images': len(list(self.raw_images_path.glob("*"))),
                'annotated_images': len(list(self.annotated_images_path.glob("*"))),
                'total_annotations': 0,
                'labels': []
            }
            
            # Verificar se existe arquivo de anotações
            annotations_file = self.training_data_path / "extracted_annotations.json"
            if annotations_file.exists():
                with open(annotations_file, 'r') as f:
                    annotations = json.load(f)
                
                stats['total_annotations'] = len(annotations)
                stats['labels'] = list(set(ann['label'] for ann in annotations))
            
            return stats
            
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return {}
    
    def clear_training_data(self) -> bool:
        """Limpa todos os dados de treinamento"""
        try:
            # Remover arquivos das pastas
            for folder in [self.raw_images_path, self.annotated_images_path]:
                for file in folder.glob("*"):
                    if file.is_file():
                        file.unlink()
            
            # Remover arquivos de anotações
            annotations_file = self.training_data_path / "extracted_annotations.json"
            if annotations_file.exists():
                annotations_file.unlink()
            
            label_mapping_file = self.training_data_path / "label_mapping.json"
            if label_mapping_file.exists():
                label_mapping_file.unlink()
            
            logger.info("Dados de treinamento limpos")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao limpar dados: {e}")
            return False
