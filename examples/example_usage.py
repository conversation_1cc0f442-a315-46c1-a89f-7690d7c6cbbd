#!/usr/bin/env python3
"""
Exemplo de uso do Bot de Automação CVAT
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import CVATBot
from loguru import logger

def exemplo_completo():
    """Exemplo completo de uso do bot"""
    
    # Inicializar bot
    bot = CVATBot()
    
    # 1. Treinar modelo com imagens de exemplo
    logger.info("=== EXEMPLO: TREINAMENTO ===")
    
    # Caminhos para imagens de exemplo (substitua pelos seus)
    raw_images = [
        "./exemplos/carro1.jpg",
        "./exemplos/carro2.jpg", 
        "./exemplos/pessoa1.jpg",
        "./exemplos/pessoa2.jpg"
    ]
    
    annotated_images = [
        "./exemplos/carro1_anotado.jpg",
        "./exemplos/carro2_anotado.jpg",
        "./exemplos/pessoa1_anotado.jpg", 
        "./exemplos/pessoa2_anotado.jpg"
    ]
    
    # Verificar se arquivos existem (para exemplo)
    existing_raw = [img for img in raw_images if os.path.exists(img)]
    existing_annotated = [img for img in annotated_images if os.path.exists(img)]
    
    if existing_raw and existing_annotated:
        success = bot.train_model(existing_raw, existing_annotated)
        if success:
            logger.info("Modelo treinado com sucesso!")
        else:
            logger.error("Falha no treinamento")
            return
    else:
        logger.warning("Imagens de exemplo não encontradas. Pulando treinamento.")
    
    # 2. Listar jobs disponíveis
    logger.info("=== EXEMPLO: LISTAGEM DE JOBS ===")
    
    jobs = bot.get_available_jobs()
    if jobs:
        logger.info(f"Encontrados {len(jobs)} jobs:")
        for job in jobs[:5]:  # Mostrar apenas os primeiros 5
            logger.info(f"  Job ID: {job['id']}, Status: {job.get('status', 'N/A')}")
    else:
        logger.info("Nenhum job encontrado")
    
    # 3. Anotar jobs (exemplo com IDs fictícios)
    logger.info("=== EXEMPLO: ANOTAÇÃO AUTOMÁTICA ===")
    
    # Para este exemplo, vamos usar IDs fictícios
    # Em uso real, use IDs reais obtidos da listagem
    example_job_ids = [1, 2]  # Substitua por IDs reais
    
    # Mapeamento de labels personalizado
    label_mappings = {
        "detected_object": "objeto_detectado",
        "red": "veiculo",
        "blue": "pessoa",
        "green": "animal"
    }
    
    logger.info(f"Tentando anotar jobs: {example_job_ids}")
    logger.info("NOTA: Este é apenas um exemplo. Use IDs de jobs reais.")
    
    # Descomente a linha abaixo para executar anotação real
    # success = bot.annotate_jobs(example_job_ids, label_mappings)
    
    # 4. Mostrar estatísticas
    logger.info("=== EXEMPLO: ESTATÍSTICAS ===")
    bot.show_statistics()

def exemplo_treinamento_simples():
    """Exemplo simples de treinamento"""
    
    bot = CVATBot()
    
    # Criar imagens de exemplo simples (para demonstração)
    import cv2
    import numpy as np
    
    # Criar diretório de exemplos
    os.makedirs("./exemplos", exist_ok=True)
    
    # Criar imagem simples com retângulo
    img = np.zeros((300, 300, 3), dtype=np.uint8)
    cv2.rectangle(img, (50, 50), (150, 150), (255, 255, 255), -1)
    cv2.imwrite("./exemplos/exemplo_raw.jpg", img)
    
    # Criar versão anotada (com retângulo vermelho)
    img_annotated = img.copy()
    cv2.rectangle(img_annotated, (45, 45), (155, 155), (0, 0, 255), 3)
    cv2.imwrite("./exemplos/exemplo_anotado.jpg", img_annotated)
    
    logger.info("Imagens de exemplo criadas em ./exemplos/")
    
    # Treinar com as imagens criadas
    success = bot.train_model(
        ["./exemplos/exemplo_raw.jpg"],
        ["./exemplos/exemplo_anotado.jpg"]
    )
    
    if success:
        logger.info("Treinamento de exemplo concluído!")
    else:
        logger.error("Falha no treinamento de exemplo")

def exemplo_configuracao():
    """Exemplo de configuração do bot"""
    
    from config import settings
    
    logger.info("=== CONFIGURAÇÕES ATUAIS ===")
    logger.info(f"CVAT URL: {settings.CVAT_URL}")
    logger.info(f"Navegador: {settings.BROWSER_TYPE}")
    logger.info(f"Modo headless: {settings.HEADLESS_MODE}")
    logger.info(f"Limiar de confiança: {settings.CONFIDENCE_THRESHOLD}")
    logger.info(f"Tamanho da imagem: {settings.IMAGE_SIZE}")
    
    logger.info("\n=== DICAS DE CONFIGURAÇÃO ===")
    logger.info("1. Configure o arquivo .env com suas credenciais CVAT")
    logger.info("2. Use HEADLESS_MODE=true para execução em servidor")
    logger.info("3. Ajuste CONFIDENCE_THRESHOLD baseado na qualidade do modelo")
    logger.info("4. Use IMAGE_SIZE menor para processamento mais rápido")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Exemplos de uso do Bot CVAT")
    parser.add_argument("--exemplo", choices=["completo", "treinamento", "config"], 
                       default="completo", help="Tipo de exemplo para executar")
    
    args = parser.parse_args()
    
    try:
        if args.exemplo == "completo":
            exemplo_completo()
        elif args.exemplo == "treinamento":
            exemplo_treinamento_simples()
        elif args.exemplo == "config":
            exemplo_configuracao()
            
    except KeyboardInterrupt:
        logger.info("Exemplo interrompido pelo usuário")
    except Exception as e:
        logger.error(f"Erro no exemplo: {e}")
        sys.exit(1)
