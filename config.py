"""
Configurações do Bot de Automação CVAT
"""
import os
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # CVAT Configuration
    CVAT_URL: str = "http://localhost:8080"
    CVAT_USERNAME: str = ""
    CVAT_PASSWORD: str = ""
    CVAT_API_TOKEN: Optional[str] = None

    # Browser Configuration
    BROWSER_TYPE: str = "chrome"  # chrome, firefox, edge
    HEADLESS_MODE: bool = False
    BROWSER_TIMEOUT: int = 30

    # Model Configuration
    MODEL_PATH: str = "./models/trained_model.h5"
    CONFIDENCE_THRESHOLD: float = 0.8
    IMAGE_SIZE: tuple = (224, 224)

    # Training Data
    TRAINING_DATA_PATH: str = "./training_data"
    ANNOTATED_IMAGES_PATH: str = "./training_data/annotated"
    RAW_IMAGES_PATH: str = "./training_data/raw"

    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/cvat_bot.log"

    # Automation Settings
    MAX_RETRIES: int = 3
    RETRY_DELAY: int = 2
    BATCH_SIZE: int = 10

    class Config:
        env_file = ".env"
        case_sensitive = True

# Instância global das configurações
settings = Settings()

# Criar diretórios necessários
os.makedirs("./models", exist_ok=True)
os.makedirs("./training_data/annotated", exist_ok=True)
os.makedirs("./training_data/raw", exist_ok=True)
os.makedirs("./logs", exist_ok=True)
