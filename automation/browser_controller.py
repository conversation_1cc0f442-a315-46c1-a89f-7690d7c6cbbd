"""
Controlador do Navegador para Automação CVAT
"""
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
import time
from typing import List, Dict, Tuple, Optional
from loguru import logger
from config import settings

class BrowserController:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def start_browser(self) -> bool:
        """Inicia o navegador"""
        try:
            if settings.BROWSER_TYPE.lower() == "chrome":
                options = webdriver.ChromeOptions()
                if settings.HEADLESS_MODE:
                    options.add_argument("--headless")
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                
                service = ChromeService(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=options)
                
            elif settings.BROWSER_TYPE.lower() == "firefox":
                options = webdriver.FirefoxOptions()
                if settings.HEADLESS_MODE:
                    options.add_argument("--headless")
                    
                service = FirefoxService(GeckoDriverManager().install())
                self.driver = webdriver.Firefox(service=service, options=options)
            
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, settings.BROWSER_TIMEOUT)
            
            logger.info(f"Navegador {settings.BROWSER_TYPE} iniciado com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao iniciar navegador: {e}")
            return False
    
    def login_cvat(self, username: str = None, password: str = None) -> bool:
        """Faz login no CVAT via interface web"""
        try:
            username = username or settings.CVAT_USERNAME
            password = password or settings.CVAT_PASSWORD
            
            # Navegar para página de login
            self.driver.get(f"{settings.CVAT_URL}/auth/login")
            
            # Aguardar elementos de login
            username_field = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            password_field = self.driver.find_element(By.NAME, "password")
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            
            # Preencher credenciais
            username_field.clear()
            username_field.send_keys(username)
            password_field.clear()
            password_field.send_keys(password)
            
            # Fazer login
            login_button.click()
            
            # Aguardar redirecionamento
            self.wait.until(EC.url_contains("/tasks"))
            
            logger.info("Login no CVAT realizado com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"Erro no login: {e}")
            return False
    
    def navigate_to_job(self, job_id: int) -> bool:
        """Navega para um job específico"""
        try:
            job_url = f"{settings.CVAT_URL}/tasks/jobs/{job_id}"
            self.driver.get(job_url)
            
            # Aguardar carregamento da interface de anotação
            self.wait.until(
                EC.presence_of_element_located((By.CLASS_NAME, "cvat-canvas-container"))
            )
            
            logger.info(f"Navegado para job {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao navegar para job {job_id}: {e}")
            return False
    
    def get_current_frame_number(self) -> int:
        """Obtém o número do frame atual"""
        try:
            frame_input = self.driver.find_element(
                By.CSS_SELECTOR, "input[role='spinbutton']"
            )
            return int(frame_input.get_attribute("value"))
        except:
            return 0
    
    def navigate_to_frame(self, frame_number: int) -> bool:
        """Navega para um frame específico"""
        try:
            frame_input = self.driver.find_element(
                By.CSS_SELECTOR, "input[role='spinbutton']"
            )
            frame_input.clear()
            frame_input.send_keys(str(frame_number))
            frame_input.send_keys("\n")
            
            time.sleep(1)  # Aguardar carregamento do frame
            return True
            
        except Exception as e:
            logger.error(f"Erro ao navegar para frame {frame_number}: {e}")
            return False
    
    def create_rectangle_annotation(self, x1: int, y1: int, x2: int, y2: int, 
                                  label_name: str) -> bool:
        """Cria uma anotação retangular"""
        try:
            # Selecionar ferramenta de retângulo
            rectangle_tool = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-icon='rectangle']"))
            )
            rectangle_tool.click()
            
            # Obter canvas
            canvas = self.driver.find_element(By.TAG_NAME, "canvas")
            
            # Criar ação de arrastar
            actions = ActionChains(self.driver)
            actions.move_to_element_with_offset(canvas, x1, y1)
            actions.click_and_hold()
            actions.move_to_element_with_offset(canvas, x2, y2)
            actions.release()
            actions.perform()
            
            # Aguardar aparição do menu de labels
            time.sleep(0.5)
            
            # Selecionar label
            label_option = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//span[text()='{label_name}']"))
            )
            label_option.click()
            
            logger.info(f"Anotação criada: ({x1},{y1}) -> ({x2},{y2}) com label '{label_name}'")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao criar anotação: {e}")
            return False
    
    def save_annotations(self) -> bool:
        """Salva as anotações"""
        try:
            # Usar Ctrl+S para salvar
            actions = ActionChains(self.driver)
            actions.key_down("Control").send_keys("s").key_up("Control").perform()
            
            time.sleep(2)  # Aguardar salvamento
            
            logger.info("Anotações salvas")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao salvar anotações: {e}")
            return False
    
    def get_canvas_dimensions(self) -> Tuple[int, int]:
        """Obtém dimensões do canvas"""
        try:
            canvas = self.driver.find_element(By.TAG_NAME, "canvas")
            width = int(canvas.get_attribute("width"))
            height = int(canvas.get_attribute("height"))
            return width, height
        except:
            return 0, 0
    
    def close_browser(self) -> None:
        """Fecha o navegador"""
        if self.driver:
            self.driver.quit()
            logger.info("Navegador fechado")
    
    def take_screenshot(self, filename: str) -> bool:
        """Tira screenshot da tela atual"""
        try:
            self.driver.save_screenshot(filename)
            return True
        except Exception as e:
            logger.error(f"Erro ao tirar screenshot: {e}")
            return False
