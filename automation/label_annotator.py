"""
Anotador Automático de Labels
"""
import os
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
from loguru import logger
import time

from models.pattern_detector import PatternDetector
from cvat_api.client import CVATClient
from automation.browser_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>roller
from config import settings

class LabelAnnotator:
    def __init__(self):
        self.pattern_detector = PatternDetector()
        self.cvat_client = CVATClient()
        self.browser_controller = BrowserController()
        self.temp_dir = "./temp_frames"
        os.makedirs(self.temp_dir, exist_ok=True)
    
    def initialize(self) -> bool:
        """Inicializa todos os componentes"""
        try:
            # Autenticar no CVAT
            if not self.cvat_client.authenticate():
                logger.error("Falha na autenticação CVAT")
                return False
            
            # Iniciar navegador
            if not self.browser_controller.start_browser():
                logger.error("Falha ao iniciar navegador")
                return False
            
            # Login no navegador
            if not self.browser_controller.login_cvat():
                logger.error("Falha no login via navegador")
                return False
            
            # Carregar modelo treinado
            self.pattern_detector.load_model()
            
            logger.info("Inicialização concluída com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"Erro na inicialização: {e}")
            return False
    
    def annotate_job(self, job_id: int, label_mappings: Dict[str, str] = None) -> bool:
        """Anota automaticamente um job completo"""
        try:
            logger.info(f"Iniciando anotação automática do job {job_id}")
            
            # Navegar para o job
            if not self.browser_controller.navigate_to_job(job_id):
                return False
            
            # Obter informações do job
            job_frames = self.cvat_client.get_job_frames(job_id)
            if not job_frames:
                logger.error("Nenhum frame encontrado no job")
                return False
            
            # Processar cada frame
            annotations_created = 0
            for frame_info in job_frames:
                frame_number = frame_info['frame_number']
                
                try:
                    # Navegar para o frame
                    self.browser_controller.navigate_to_frame(frame_number)
                    
                    # Baixar frame atual
                    frame_path = os.path.join(self.temp_dir, f"frame_{frame_number}.jpg")
                    if self.cvat_client.download_frame(job_id, frame_number, frame_path):
                        
                        # Detectar objetos no frame
                        detections = self.pattern_detector.predict(frame_path)
                        
                        # Criar anotações para cada detecção
                        for detection in detections:
                            if detection['confidence'] > settings.CONFIDENCE_THRESHOLD:
                                
                                # Converter detecção para coordenadas de anotação
                                bbox = self._detection_to_bbox(detection, frame_path)
                                if bbox:
                                    label_name = label_mappings.get(
                                        detection['label'], 
                                        detection['label']
                                    ) if label_mappings else detection['label']
                                    
                                    # Criar anotação no navegador
                                    if self.browser_controller.create_rectangle_annotation(
                                        bbox[0], bbox[1], bbox[2], bbox[3], label_name
                                    ):
                                        annotations_created += 1
                                        logger.info(
                                            f"Anotação criada no frame {frame_number}: "
                                            f"{label_name} ({detection['confidence']:.2f})"
                                        )
                        
                        # Limpar arquivo temporário
                        os.remove(frame_path)
                    
                    # Pequena pausa entre frames
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"Erro ao processar frame {frame_number}: {e}")
                    continue
            
            # Salvar todas as anotações
            if annotations_created > 0:
                self.browser_controller.save_annotations()
                logger.info(f"Job {job_id} anotado com {annotations_created} anotações")
                return True
            else:
                logger.warning(f"Nenhuma anotação criada para o job {job_id}")
                return False
                
        except Exception as e:
            logger.error(f"Erro na anotação do job {job_id}: {e}")
            return False
    
    def _detection_to_bbox(self, detection: Dict, image_path: str) -> Optional[Tuple[int, int, int, int]]:
        """Converte detecção do modelo para bounding box"""
        try:
            # Carregar imagem para obter dimensões
            image = cv2.imread(image_path)
            if image is None:
                return None
            
            height, width = image.shape[:2]
            
            # Por enquanto, implementação básica
            # Em uma implementação real, o modelo deveria retornar coordenadas
            # Aqui estamos simulando uma detecção central
            center_x = width // 2
            center_y = height // 2
            box_width = min(width // 4, 100)
            box_height = min(height // 4, 100)
            
            x1 = max(0, center_x - box_width // 2)
            y1 = max(0, center_y - box_height // 2)
            x2 = min(width, center_x + box_width // 2)
            y2 = min(height, center_y + box_height // 2)
            
            return (x1, y1, x2, y2)
            
        except Exception as e:
            logger.error(f"Erro ao converter detecção: {e}")
            return None
    
    def batch_annotate_jobs(self, job_ids: List[int], 
                           label_mappings: Dict[str, str] = None) -> Dict[int, bool]:
        """Anota múltiplos jobs em lote"""
        results = {}
        
        for job_id in job_ids:
            try:
                logger.info(f"Processando job {job_id}")
                results[job_id] = self.annotate_job(job_id, label_mappings)
                
                # Pausa entre jobs
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Erro ao processar job {job_id}: {e}")
                results[job_id] = False
        
        return results
    
    def validate_annotations(self, job_id: int) -> Dict[str, Any]:
        """Valida anotações criadas"""
        try:
            annotations = self.cvat_client.get_job_annotations(job_id)
            
            validation_result = {
                'total_annotations': len(annotations.get('shapes', [])),
                'frames_annotated': len(set(
                    shape['frame'] for shape in annotations.get('shapes', [])
                )),
                'labels_used': list(set(
                    shape['label_id'] for shape in annotations.get('shapes', [])
                )),
                'valid': True
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Erro na validação: {e}")
            return {'valid': False, 'error': str(e)}
    
    def cleanup(self) -> None:
        """Limpa recursos"""
        try:
            self.browser_controller.close_browser()
            
            # Limpar arquivos temporários
            if os.path.exists(self.temp_dir):
                for file in os.listdir(self.temp_dir):
                    os.remove(os.path.join(self.temp_dir, file))
                os.rmdir(self.temp_dir)
            
            logger.info("Limpeza concluída")
            
        except Exception as e:
            logger.error(f"Erro na limpeza: {e}")
    
    def get_job_statistics(self, job_id: int) -> Dict[str, Any]:
        """Obtém estatísticas de um job"""
        try:
            annotations = self.cvat_client.get_job_annotations(job_id)
            frames = self.cvat_client.get_job_frames(job_id)
            
            stats = {
                'total_frames': len(frames),
                'annotated_frames': len(set(
                    shape['frame'] for shape in annotations.get('shapes', [])
                )),
                'total_annotations': len(annotations.get('shapes', [])),
                'completion_rate': 0
            }
            
            if stats['total_frames'] > 0:
                stats['completion_rate'] = (
                    stats['annotated_frames'] / stats['total_frames']
                ) * 100
            
            return stats
            
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return {}
