#!/usr/bin/env python3
"""
Testes básicos para o Bot de Automação CVAT
"""
import unittest
import sys
import os
import tempfile
import shutil
from pathlib import Path

# Adicionar diretório raiz ao path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings
from models.pattern_detector import PatternDetector
from data.training_manager import TrainingDataManager
from cvat_api.client import CVATClient
from utils.image_processor import ImageProcessor

class TestBasicFunctionality(unittest.TestCase):
    """Testes básicos de funcionalidade"""
    
    def setUp(self):
        """Configuração para cada teste"""
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
    
    def test_config_loading(self):
        """Testa carregamento de configurações"""
        self.assertIsNotNone(settings.CVAT_URL)
        self.assertIsNotNone(settings.IMAGE_SIZE)
        self.assertIsInstance(settings.CONFIDENCE_THRESHOLD, float)
        self.assertTrue(0 <= settings.CONFIDENCE_THRESHOLD <= 1)
    
    def test_pattern_detector_initialization(self):
        """Testa inicialização do detector de padrões"""
        detector = PatternDetector()
        self.assertIsNotNone(detector)
        self.assertEqual(detector.image_size, settings.IMAGE_SIZE)
    
    def test_training_manager_initialization(self):
        """Testa inicialização do gerenciador de treinamento"""
        manager = TrainingDataManager()
        self.assertIsNotNone(manager)
        self.assertTrue(manager.training_data_path.exists())
    
    def test_cvat_client_initialization(self):
        """Testa inicialização do cliente CVAT"""
        client = CVATClient()
        self.assertIsNotNone(client)
        self.assertEqual(client.base_url, settings.CVAT_URL)
    
    def test_image_processor_resize(self):
        """Testa redimensionamento de imagem"""
        import cv2
        import numpy as np
        
        # Criar imagem de teste
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # Redimensionar
        resized = ImageProcessor.resize_image(test_image, (50, 50))
        
        self.assertEqual(resized.shape[:2], (50, 50))
    
    def test_image_validation(self):
        """Testa validação de imagens"""
        # Criar imagem de teste válida
        import cv2
        import numpy as np
        
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        test_path = os.path.join(self.temp_dir, "test_image.jpg")
        cv2.imwrite(test_path, test_image)
        
        # Testar validação
        self.assertTrue(ImageProcessor.validate_image(test_path))
        self.assertFalse(ImageProcessor.validate_image("arquivo_inexistente.jpg"))
    
    def test_directory_creation(self):
        """Testa criação de diretórios necessários"""
        required_dirs = [
            "./models",
            "./training_data/raw", 
            "./training_data/annotated",
            "./logs"
        ]
        
        for dir_path in required_dirs:
            self.assertTrue(os.path.exists(dir_path), f"Diretório {dir_path} não existe")

class TestImageProcessing(unittest.TestCase):
    """Testes de processamento de imagens"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
    
    def test_color_region_extraction(self):
        """Testa extração de regiões coloridas"""
        import cv2
        import numpy as np
        
        # Criar imagem com retângulo vermelho
        image = np.zeros((200, 200, 3), dtype=np.uint8)
        cv2.rectangle(image, (50, 50), (150, 150), (0, 0, 255), -1)
        
        # Definir range para vermelho
        color_ranges = {
            'red': ([0, 50, 50], [10, 255, 255])
        }
        
        regions = ImageProcessor.extract_color_regions(image, color_ranges)
        
        self.assertIn('red', regions)
        self.assertGreater(len(regions['red']), 0)
    
    def test_annotation_overlay(self):
        """Testa criação de overlay com anotações"""
        import cv2
        import numpy as np
        
        # Criar imagem de teste
        image = np.zeros((200, 200, 3), dtype=np.uint8)
        
        # Criar anotações de teste
        annotations = [
            {
                'bbox': [50, 50, 150, 150],
                'label': 'pessoa',
                'confidence': 0.95
            }
        ]
        
        overlay = ImageProcessor.create_annotation_overlay(image, annotations)
        
        # Verificar se overlay foi criado (não é igual à imagem original)
        self.assertFalse(np.array_equal(image, overlay))

class TestTrainingDataManager(unittest.TestCase):
    """Testes do gerenciador de dados de treinamento"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
        
        # Configurar manager com diretório temporário
        self.manager = TrainingDataManager()
        self.manager.training_data_path = Path(self.temp_dir)
        self.manager.raw_images_path = Path(self.temp_dir) / "raw"
        self.manager.annotated_images_path = Path(self.temp_dir) / "annotated"
        
        # Criar diretórios
        self.manager.raw_images_path.mkdir(exist_ok=True)
        self.manager.annotated_images_path.mkdir(exist_ok=True)
    
    def test_statistics_empty(self):
        """Testa estatísticas com dados vazios"""
        stats = self.manager.get_training_statistics()
        
        self.assertEqual(stats['raw_images'], 0)
        self.assertEqual(stats['annotated_images'], 0)
        self.assertEqual(stats['total_annotations'], 0)
    
    def test_add_training_images(self):
        """Testa adição de imagens de treinamento"""
        import cv2
        import numpy as np
        
        # Criar imagem de teste
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        test_path = os.path.join(self.temp_dir, "test_input.jpg")
        cv2.imwrite(test_path, test_image)
        
        # Adicionar ao manager
        success = self.manager.add_training_images([test_path])
        
        self.assertTrue(success)
        
        # Verificar se foi copiada
        stats = self.manager.get_training_statistics()
        self.assertEqual(stats['raw_images'], 1)

def run_tests():
    """Executa todos os testes"""
    # Configurar logging para testes
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    # Criar suite de testes
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Adicionar testes
    suite.addTests(loader.loadTestsFromTestCase(TestBasicFunctionality))
    suite.addTests(loader.loadTestsFromTestCase(TestImageProcessing))
    suite.addTests(loader.loadTestsFromTestCase(TestTrainingDataManager))
    
    # Executar testes
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
